# AI Web Tester Package

A comprehensive AI-powered web testing package that uses browser automation and AI models to systematically test web applications.

## Features

- **AI-Powered Testing**: Uses AI models (DeepSeek, OpenAI) to intelligently test web applications
- **Browser Automation**: Built on Playwright for reliable web interactions
- **Systematic Testing**: Follows structured testing patterns for comprehensive coverage
- **Local Browser-Use**: Includes local browser-use library for better control
- **Flexible Configuration**: Support for various AI models and testing parameters

## Installation

```bash
pip install ai-web-tester
```

## Quick Start

### Basic Usage

```python
from ai_web_tester_pkg import run_web_test

# Test a website with DeepSeek
result = run_web_test(
    target_url="https://example.com",
    api_key="your-deepseek-api-key",
    model="deepseek-chat"
)

print(f"Test status: {result['status']}")
```

### Advanced Usage

```python
from ai_web_tester_pkg import run_web_test

# Test with custom configuration
result = run_web_test(
    target_url="https://example.com",
    cdp_url="ws://localhost:9222",  # Connect to existing Chrome instance
    api_key="your-api-key",
    model="gpt-4",
    base_url="https://api.openai.com/v1",
    headless=True,
    prompt_version="get_all_v2"
)
```

## API Reference

### `run_web_test()`

Main function to run web testing.

**Parameters:**
- `target_url` (str): The URL to test
- `cdp_url` (str, optional): Chrome DevTools Protocol URL
- `api_key` (str, optional): API key for the AI model
- `model` (str): Model name (default: "deepseek-chat")
- `base_url` (str, optional): Base URL for the API
- `headless` (bool): Whether to run browser in headless mode
- `prompt_version` (str): Version of the prompt template to use
- `log_dir` (Path, optional): Directory for logs

**Returns:**
Dictionary containing test results with keys:
- `status`: Test status ('completed', 'failed', 'running')
- `target_url`: The tested URL
- `start_time`: Test start time
- `end_time`: Test end time
- `agent_result`: Detailed agent execution results
- `total_steps`: Number of testing steps performed

## Environment Variables

You can set API keys using environment variables:

```bash
export DEEPSEEK_API_KEY="your-deepseek-api-key"
# or
export OPENAI_API_KEY="your-openai-api-key"
```

## Command Line Usage

After installation, you can use the command line interface:

```bash
ai-web-tester --url https://example.com --api-key your-key --model deepseek-chat
```

## Supported AI Models

- **DeepSeek**: `deepseek-chat` (default)
- **OpenAI**: `gpt-4`, `gpt-3.5-turbo`
- **Custom**: Any OpenAI-compatible API

## License

MIT License

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.