"""
Setup script for AI Web Tester Package
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding='utf-8') if (this_directory / "README.md").exists() else ""

setup(
    name="ai-web-tester",
    version="1.0.0",
    author="AI Web Tester Team",
    author_email="<EMAIL>",
    description="AI-powered web testing package with browser automation",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/ai-web-tester/ai-web-tester",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Software Development :: Testing",
        "Topic :: Internet :: WWW/HTTP :: Browsers",
    ],
    python_requires=">=3.8",
    install_requires=[
        "openai>=1.0.0",
        "playwright>=1.40.0",
        "pyyaml>=6.0",
        "python-dotenv>=1.0.0",
        "colorlog>=6.0.0",
        "aiofiles>=23.0.0",
        "pydantic>=2.0.0",
        "httpx>=0.25.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "ai-web-tester=ai_web_tester_pkg.tester:main",
        ],
    },
    include_package_data=True,
    package_data={
        "ai_web_tester_pkg": [
            "prompts/*",
            "browser_use_local/**/*",
        ],
    },
)