import os
from typing import TYPE_CHECKING

from .logging_config import setup_logging

# Only set up logging if not in MCP mode or if explicitly requested
if os.environ.get('BROWSER_USE_SETUP_LOGGING', 'true').lower() != 'false':
	from .config import CONFIG

	# Get log file paths from config/environment
	debug_log_file = getattr(CONFIG, 'BROWSER_USE_DEBUG_LOG_FILE', None)
	info_log_file = getattr(CONFIG, 'BROWSER_USE_INFO_LOG_FILE', None)
	log_result_file = getattr(CONFIG, 'BROWSER_USE_LOG_RESULT_FILE', None)

	# Set up logging with file handlers if specified
	logger = setup_logging(debug_log_file=debug_log_file, info_log_file=info_log_file, result_log_file=log_result_file)
else:
	import logging

	logger = logging.getLogger('browser_use')

# Monkeypatch BaseSubprocessTransport.__del__ to handle closed event loops gracefully
from asyncio import base_subprocess

_original_del = base_subprocess.BaseSubprocessTransport.__del__


def _patched_del(self):
	"""Patched __del__ that handles closed event loops without throwing noisy red-herring errors like RuntimeError: Event loop is closed"""
	try:
		# Check if the event loop is closed before calling the original
		if hasattr(self, '_loop') and self._loop and self._loop.is_closed():
			# Event loop is closed, skip cleanup that requires the loop
			return
		_original_del(self)
	except RuntimeError as e:
		if 'Event loop is closed' in str(e):
			# Silently ignore this specific error
			pass
		else:
			raise


base_subprocess.BaseSubprocessTransport.__del__ = _patched_del


# Type stubs for lazy imports - fixes linter warnings
if TYPE_CHECKING:
	from .agent.prompts import SystemPrompt
	from .agent.service import Agent
	from .agent.views import ActionModel, ActionResult, AgentHistoryList
	from .browser import BrowserProfile, BrowserSession
	from .browser import BrowserSession as Browser
	from .controller.service import Controller
	from .dom.service import DomService
	from .llm.anthropic.chat import ChatAnthropic
	from .llm.azure.chat import ChatAzureOpenAI
	from .llm.google.chat import ChatGoogle
	from .llm.groq.chat import ChatGroq
	from .llm.ollama.chat import ChatOllama
	from .llm.openai.chat import ChatOpenAI


# Lazy imports mapping - only import when actually accessed
_LAZY_IMPORTS = {
	# Agent service (heavy due to dependencies)
	'Agent': ('ai_web_tester_pkg.browser_use_local.agent.service', 'Agent'),
	# System prompt (moderate weight due to agent.views imports)
	'SystemPrompt': ('ai_web_tester_pkg.browser_use_local.agent.prompts', 'SystemPrompt'),
	# Agent views (very heavy - over 1 second!)
	'ActionModel': ('ai_web_tester_pkg.browser_use_local.agent.views', 'ActionModel'),
	'ActionResult': ('ai_web_tester_pkg.browser_use_local.agent.views', 'ActionResult'),
	'AgentHistoryList': ('ai_web_tester_pkg.browser_use_local.agent.views', 'AgentHistoryList'),
	# Browser components (heavy due to playwright/patchright)
	'BrowserSession': ('ai_web_tester_pkg.browser_use_local.browser', 'BrowserSession'),
	'Browser': ('ai_web_tester_pkg.browser_use_local.browser', 'BrowserSession'),  # Alias for BrowserSession
	'BrowserProfile': ('ai_web_tester_pkg.browser_use_local.browser', 'BrowserProfile'),
	# Controller (moderate weight)
	'Controller': ('ai_web_tester_pkg.browser_use_local.controller.service', 'Controller'),
	# DOM service (moderate weight)
	'DomService': ('ai_web_tester_pkg.browser_use_local.dom.service', 'DomService'),
	# Chat models (very heavy imports)
	'ChatOpenAI': ('ai_web_tester_pkg.browser_use_local.llm.openai.chat', 'ChatOpenAI'),
	'ChatGoogle': ('ai_web_tester_pkg.browser_use_local.llm.google.chat', 'ChatGoogle'),
	'ChatAnthropic': ('ai_web_tester_pkg.browser_use_local.llm.anthropic.chat', 'ChatAnthropic'),
	'ChatGroq': ('ai_web_tester_pkg.browser_use_local.llm.groq.chat', 'ChatGroq'),
	'ChatAzureOpenAI': ('ai_web_tester_pkg.browser_use_local.llm.azure.chat', 'ChatAzureOpenAI'),
	'ChatOllama': ('ai_web_tester_pkg.browser_use_local.llm.ollama.chat', 'ChatOllama'),
}


def __getattr__(name: str):
	"""Lazy import mechanism - only import modules when they're actually accessed."""
	if name in _LAZY_IMPORTS:
		module_path, attr_name = _LAZY_IMPORTS[name]
		try:
			from importlib import import_module

			module = import_module(module_path)
			attr = getattr(module, attr_name)
			# Cache the imported attribute in the module's globals
			globals()[name] = attr
			return attr
		except ImportError as e:
			raise ImportError(f'Failed to import {name} from {module_path}: {e}') from e

	raise AttributeError(f"module '{__name__}' has no attribute '{name}'")


__all__ = [
	'Agent',
	'BrowserSession',
	'Browser',  # Alias for BrowserSession
	'BrowserProfile',
	'Controller',
	'DomService',
	'SystemPrompt',
	'ActionResult',
	'ActionModel',
	'AgentHistoryList',
	# Chat models
	'ChatOpenAI',
	'ChatGoogle',
	'ChatAnthropic',
	'ChatGroq',
	'ChatAzureOpenAI',
	'ChatOllama',
]
