#!/home/<USER>/pro/pyvenv/bin/python
"""
Simple setup test for AI Web Tester
"""

import sys
import importlib
import logging
import traceback
from pathlib import Path

# Add the current directory to Python path to allow importing the package
sys.path.insert(0, str(Path(__file__).parent))

from tester import run_web_test

# Create log directory
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / 'setup_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def main():
    
    
    res = run_web_test(
        target_url="http://127.0.0.1:3000",
        api_key="sk-6ee1d20388964f4dbb5dcbd8b3e16325",
        model="qwen-vl-max-latest",
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        cdp_url="http://localhost:3002"
    )
    print(res)

if __name__ == "__main__":
    sys.exit(main())