"""
AI Web Tester - Main Entry Point

This module provides the main interface for the AI Web Tester package.
"""

import asyncio
import os
import sys
import logging
import traceback
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

# Import local browser-use library
from .browser_use_local import Agent, BrowserProfile, BrowserSession
from .browser_use_local.llm import ChatDeepSeek, ChatOpenAI, BaseChatModel

# Setup logging
def setup_logging(log_dir: Optional[Path] = None) -> logging.Logger:
    """Setup logging configuration"""
    if log_dir is None:
        log_dir = Path.cwd() / "logs"

    log_dir.mkdir(exist_ok=True)

    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler(log_dir / 'ai_web_tester.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


class AIWebTester:
    """AI-powered web testing agent"""

    def __init__(self,
                 api_key: str,
                 model: str = "deepseek-chat",
                 base_url: Optional[str] = None,
                 log_dir: Optional[Path] = None):
        """
        Initialize the AI Web Tester

        Args:
            api_key: API key for the AI model
            model: Model name (default: deepseek-chat)
            base_url: Base URL for the API (optional)
            log_dir: Directory for logs (optional)
        """
        self.logger = setup_logging(log_dir)
        self.api_key = api_key
        self.model = model
        self.base_url = base_url
        self.client = self._setup_client()

    def _setup_client(self) -> BaseChatModel:
        """Setup the AI client"""
        try:
            client_kwargs = {
                'api_key': self.api_key,
                'model': self.model
            }

            if self.base_url:
                client_kwargs['base_url'] = self.base_url

            self.logger.info(f"Setting up AI client with model: {self.model}")

            # Choose client based on model
            if "deepseek" in self.model.lower():
                return ChatDeepSeek(**client_kwargs)
            else:
                return ChatOpenAI(**client_kwargs)

        except Exception as e:
            self.logger.error(f"Failed to setup AI client: {e}")
            self.logger.error(f"Traceback:\n{traceback.format_exc()}")
            raise e

    def _load_prompt(self, prompt_version: str = "get_all_v2") -> str:
        """Load testing prompt template"""
        try:
            prompt_file = Path(__file__).parent / "prompts" / prompt_version

            if not prompt_file.exists():
                self.logger.warning(f"Prompt file {prompt_file} not found, using default")
                return "你是一个智能网页交互测试器。请系统性地探索和测试目标网页的所有交互元素。目标网站: $TARGET$"

            with open(prompt_file, 'r', encoding='utf-8') as f:
                prompt = f.read()

            self.logger.info(f"Loaded prompt from: {prompt_file}")
            return prompt

        except Exception as e:
            self.logger.error(f"Failed to load prompt: {e}")
            # return "你是一个智能网页交互测试器。请系统性地探索和测试目标网页的所有交互元素。目标网站: $TARGET$"
            raise e
    async def test_website(self,
                          target_url: str,
                          cdp_url: Optional[str] = None,
                          headless: bool = False,
                          prompt_version: str = "get_all_v2") -> Dict[str, Any]:
        """
        Test a website using AI agent

        Args:
            target_url: The URL to test
            cdp_url: Chrome DevTools Protocol URL (optional)
            headless: Whether to run browser in headless mode
            prompt_version: Version of the prompt template to use

        Returns:
            Dictionary containing test results
        """
        self.logger.info(f"Starting test for: {target_url}")

        start_time = datetime.now()
        result = {
            'target_url': target_url,
            'cdp_url': cdp_url,
            'start_time': start_time.isoformat(),
            'status': 'running',
            'interactions': [],
            'errors': [],
            'screenshots': []
        }

        try:
            # Load and prepare prompt
            prompt = self._load_prompt(prompt_version)
            prompt = prompt.replace('$TARGET$', target_url)

            # Setup browser profile
            # logger.info(f"Creating browser agent for URL: {url}")
            # Create browser-use agent
            profile = BrowserProfile(
			    # is_local=False,
                cdp_url = cdp_url,
	            auto_download_pdfs = False,
                disable_security = True,
                # profile_directory = '/tmp/profile1',
                # executable_path = '/home/<USER>/.cache/ms-playwright/chromium-1091/chrome-linux/chrome',
                enable_default_extensions=False  # 明确禁用 stealth 模式

            )

            browser_session = BrowserSession(
                browser_profile=profile

            )
            agent = Agent(

                task=prompt,
                llm=self.client,
                browser_session=browser_session,
				preload=True
                # headless=self.config['browser']['headless']
            )

            # logger.info(f"Running agent for URL: {url}")
            # Run the agent


            from cdp_use.cdp.page.events import  JavascriptDialogOpeningEvent
            def on_javascriptDialogOpening(event: JavascriptDialogOpeningEvent, session_id) -> None:
                print(event)

                # print(f"Frame {event['frameId']} attached to {event['parentFrameId']}")



            async def my_step_hook(agent: Agent):
                cdp_session = await agent.browser_session.get_or_create_cdp_session()

                cdp_session.cdp_client.register.Page.javascriptDialogOpening(on_javascriptDialogOpening)



            # agent.on_step_start = my_step_hook
            # agent_result = await agent.run(on_step    _start=my_step_hook)
            agent_result = await agent.run()


        except Exception as e:
            result.update({
                'status': 'failed',
                'end_time': datetime.now().isoformat(),
                'error': str(e),
                'traceback': traceback.format_exc()
            })
            self.logger.error(f"Test failed for {target_url}: {e}")
            self.logger.error(f"Traceback:\n{traceback.format_exc()}")

        return result


def run_web_test(target_url: str,
                 cdp_url: Optional[str] = None,
                 api_key: Optional[str] = None,
                 model: str = "deepseek-chat",
                 base_url: Optional[str] = None,
                 headless: bool = False,
                 prompt_version: str = "get_all_v2",
                 log_dir: Optional[Path] = None) -> Dict[str, Any]:
    """
    Main function to run web testing

    Args:
        target_url: The URL to test
        cdp_url: Chrome DevTools Protocol URL (optional)
        api_key: API key for the AI model (if not provided, will try environment variables)
        model: Model name (default: deepseek-chat)
        base_url: Base URL for the API (optional, will use default for DeepSeek if not provided)
        headless: Whether to run browser in headless mode
        prompt_version: Version of the prompt template to use
        log_dir: Directory for logs (optional)

    Returns:
        Dictionary containing test results

    Example:
        >>> result = run_web_test(
        ...     target_url="https://example.com",
        ...     api_key="your-api-key",
        ...     model="deepseek-chat"
        ... )
        >>> print(result['status'])
        'completed'
    """
    # Get API key from parameter or environment
    if api_key is None:
        api_key = os.getenv('OPENAI_API_KEY') or os.getenv('DEEPSEEK_API_KEY')
        if api_key is None:
            raise ValueError("API key not provided. Set api_key parameter or OPENAI_API_KEY/DEEPSEEK_API_KEY environment variable")

    # Set default base URL for DeepSeek if not provided
    if base_url is None and "deepseek" in model.lower():
        base_url = "https://api.deepseek.com"

    # Create tester instance
    tester = AIWebTester(
        api_key=api_key,
        model=model,
        base_url=base_url,
        log_dir=log_dir
    )

    # Run the test
    return asyncio.run(tester.test_website(
        target_url=target_url,
        cdp_url=cdp_url,
        headless=headless,
        prompt_version=prompt_version
    ))


# For backward compatibility and direct script execution
async def main():
    """Main function for direct script execution"""
    import argparse

    parser = argparse.ArgumentParser(description='AI Web Tester')
    parser.add_argument('--url', required=True, help='Target URL to test')
    parser.add_argument('--cdp-url', help='Chrome DevTools Protocol URL')
    parser.add_argument('--api-key', help='API key for AI model')
    parser.add_argument('--model', default='deepseek-chat', help='AI model to use')
    parser.add_argument('--base-url', help='Base URL for API')
    parser.add_argument('--headless', action='store_true', help='Run browser in headless mode')
    parser.add_argument('--prompt-version', default='get_all_v2', help='Prompt template version')

    args = parser.parse_args()

    result = run_web_test(
        target_url=args.url,
        cdp_url=args.cdp_url,
        api_key=args.api_key,
        model=args.model,
        base_url=args.base_url,
        headless=args.headless,
        prompt_version=args.prompt_version
    )

    print(f"Test completed with status: {result['status']}")
    if result['status'] == 'failed':
        print(f"Error: {result.get('error', 'Unknown error')}")


if __name__ == "__main__":
    asyncio.run(main())